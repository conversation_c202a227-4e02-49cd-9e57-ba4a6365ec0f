# Инструкции по отладке синхронизации корзины

## Проблема
Товары добавляются на страницу результатов квиза, но не попадают в реальную корзину Shopify.

## Шаги для отладки

### 1. Проверьте консоль браузера
1. Откройте страницу результатов квиза
2. Откройте Developer Tools (F12)
3. Перейдите на вкладку Console
4. Пройдите квиз или обновите страницу
5. Ищите сообщения:
   - `Quiz completed, adding product to cart:`
   - `Adding to cart:`
   - `Add to cart response status:`
   - `Successfully added to cart:` или ошибки

### 2. Используйте debug-cart.html
1. Откройте файл `debug-cart.html` в браузере
2. Нажмите "Check Cart" чтобы увидеть текущее состояние корзины
3. Попробуйте добавить товар с известным variant_id
4. Проверьте, работают ли базовые операции с корзиной

### 3. Проверьте variant_id
В консоли должно появиться сообщение:
```
Product details from quiz: {variant_id: 123456, ...}
```
Убедитесь, что variant_id не null и не undefined.

### 4. Проверьте API ответы
Ищите в консоли:
- `Quiz API response:` - ответ от Quizell API
- `Add to cart response status:` - статус ответа от Shopify

### 5. Возможные проблемы и решения

#### Проблема: variant_id отсутствует или неправильный
**Симптомы**: Ошибка "Invalid variant ID" или 404
**Решение**: 
- Проверьте настройки в Quizell API
- Убедитесь, что товар существует в Shopify
- Проверьте, что variant активен и доступен

#### Проблема: CSRF токен
**Симптомы**: Ошибка 422 или "Invalid authenticity token"
**Решение**: Добавлен заголовок `X-Requested-With: XMLHttpRequest`

#### Проблема: Товар уже в корзине
**Симптомы**: Ошибка "Product already in cart"
**Решение**: Код обрабатывает эту ошибку как успех

#### Проблема: Недостаточно товара на складе
**Симптомы**: Ошибка "Not enough inventory"
**Решение**: Проверьте наличие товара в Shopify

### 6. Тестирование вручную

#### Тест 1: Добавление через API
```javascript
// В консоли браузера:
fetch('/cart/add.js', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    id: 'VARIANT_ID_HERE', // замените на реальный ID
    quantity: 1
  })
})
.then(response => response.json())
.then(data => console.log('Success:', data))
.catch(error => console.error('Error:', error));
```

#### Тест 2: Проверка корзины
```javascript
// В консоли браузера:
fetch('/cart.js')
.then(response => response.json())
.then(cart => console.log('Cart:', cart));
```

### 7. Проверьте настройки темы
1. Убедитесь, что в теме включен Ajax Cart
2. Проверьте, что нет конфликтов с другими скриптами
3. Убедитесь, что `ajaxCart` объект доступен

### 8. Проверьте сетевые запросы
1. В Developer Tools перейдите на вкладку Network
2. Обновите страницу результатов
3. Ищите запросы к `/cart/add.js` и `/cart.js`
4. Проверьте статус ответов и содержимое

### 9. Альтернативный метод добавления
Если JSON API не работает, попробуйте форму:
```javascript
const form = document.createElement('form');
form.method = 'POST';
form.action = '/cart/add';
form.innerHTML = `
  <input type="hidden" name="id" value="VARIANT_ID">
  <input type="hidden" name="quantity" value="1">
`;
document.body.appendChild(form);
form.submit();
```

## Логи для анализа

Отправьте следующие логи из консоли:
1. Полный ответ от Quiz API
2. Все сообщения связанные с добавлением в корзину
3. Ошибки, если они есть
4. Результат проверки корзины после добавления

## Контакты для поддержки

Если проблема не решается, предоставьте:
1. Скриншоты консоли с ошибками
2. URL страницы результатов
3. Variant ID товара, который должен добавляться
4. Результат выполнения debug-cart.html
