<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cart Sync Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
</head>
<body>
    <h1>Cart Synchronization Test</h1>
    
    <div id="test-results">
        <h2>Test Results:</h2>
        <ul id="results-list"></ul>
    </div>
    
    <div id="cart-contents">
        <h2>Current Cart Contents:</h2>
        <div id="cart-items"></div>
    </div>
    
    <button id="run-tests">Run Tests</button>
    <button id="clear-cart">Clear Cart</button>
    
    <script>
        // Test functions for cart synchronization
        function logResult(test, result, details = '') {
            const status = result ? '✅ PASS' : '❌ FAIL';
            const listItem = `<li>${status}: ${test} ${details}</li>`;
            $('#results-list').append(listItem);
            console.log(`${status}: ${test}`, details);
        }
        
        function displayCartContents(cart) {
            let html = '<ul>';
            if (cart.items && cart.items.length > 0) {
                cart.items.forEach(item => {
                    html += `<li>
                        ${item.product_title} - 
                        Variant: ${item.variant_id} - 
                        Qty: ${item.quantity} - 
                        Price: ${item.price}
                    </li>`;
                });
            } else {
                html += '<li>Cart is empty</li>';
            }
            html += '</ul>';
            $('#cart-items').html(html);
        }
        
        async function testCartFetch() {
            try {
                const response = await fetch('/cart.js');
                const cart = await response.json();
                displayCartContents(cart);
                logResult('Cart Fetch', true, `Found ${cart.items.length} items`);
                return cart;
            } catch (error) {
                logResult('Cart Fetch', false, error.message);
                return null;
            }
        }
        
        async function testAddToCart() {
            try {
                // Try to add a test product (you'll need to replace with actual variant ID)
                const testVariantId = 123456; // Replace with actual variant ID
                const response = await fetch('/cart/add.js', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        id: testVariantId,
                        quantity: 1
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    logResult('Add to Cart', true, `Added variant ${testVariantId}`);
                    return true;
                } else {
                    logResult('Add to Cart', false, `HTTP ${response.status}`);
                    return false;
                }
            } catch (error) {
                logResult('Add to Cart', false, error.message);
                return false;
            }
        }
        
        async function testUpdateCart() {
            try {
                const testVariantId = 123456; // Replace with actual variant ID
                const response = await fetch('/cart/update.js', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        updates: {
                            [testVariantId]: 2
                        }
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    logResult('Update Cart', true, `Updated variant ${testVariantId} to qty 2`);
                    return true;
                } else {
                    logResult('Update Cart', false, `HTTP ${response.status}`);
                    return false;
                }
            } catch (error) {
                logResult('Update Cart', false, error.message);
                return false;
            }
        }
        
        async function testRemoveFromCart() {
            try {
                const testVariantId = 123456; // Replace with actual variant ID
                const response = await fetch('/cart/update.js', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        updates: {
                            [testVariantId]: 0
                        }
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    logResult('Remove from Cart', true, `Removed variant ${testVariantId}`);
                    return true;
                } else {
                    logResult('Remove from Cart', false, `HTTP ${response.status}`);
                    return false;
                }
            } catch (error) {
                logResult('Remove from Cart', false, error.message);
                return false;
            }
        }
        
        async function clearCart() {
            try {
                const cart = await testCartFetch();
                if (cart && cart.items.length > 0) {
                    const updates = {};
                    cart.items.forEach(item => {
                        updates[item.variant_id] = 0;
                    });
                    
                    const response = await fetch('/cart/update.js', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ updates })
                    });
                    
                    if (response.ok) {
                        logResult('Clear Cart', true, 'Cart cleared successfully');
                        await testCartFetch(); // Refresh display
                    } else {
                        logResult('Clear Cart', false, `HTTP ${response.status}`);
                    }
                } else {
                    logResult('Clear Cart', true, 'Cart was already empty');
                }
            } catch (error) {
                logResult('Clear Cart', false, error.message);
            }
        }
        
        async function runAllTests() {
            $('#results-list').empty();
            console.log('Starting cart synchronization tests...');
            
            // Test 1: Fetch current cart
            await testCartFetch();
            
            // Test 2: Add to cart (commented out to avoid adding random products)
            // await testAddToCart();
            
            // Test 3: Update cart (commented out)
            // await testUpdateCart();
            
            // Test 4: Remove from cart (commented out)
            // await testRemoveFromCart();
            
            console.log('Tests completed!');
        }
        
        // Event listeners
        $('#run-tests').click(runAllTests);
        $('#clear-cart').click(clearCart);
        
        // Run initial cart fetch on page load
        $(document).ready(() => {
            testCartFetch();
        });
    </script>
</body>
</html>
