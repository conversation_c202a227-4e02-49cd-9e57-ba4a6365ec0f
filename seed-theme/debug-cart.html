<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Cart</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { margin: 5px; padding: 10px 15px; }
        input { margin: 5px; padding: 5px; }
    </style>
</head>
<body>
    <h1>Cart Debug Tool</h1>
    
    <div class="debug-section">
        <h2>Current Cart Status</h2>
        <button onclick="checkCart()">Check Cart</button>
        <div id="cart-status"></div>
    </div>
    
    <div class="debug-section">
        <h2>Add Product to Cart</h2>
        <input type="number" id="variant-id" placeholder="Variant ID" value="">
        <input type="number" id="quantity" placeholder="Quantity" value="1">
        <button onclick="addToCart()">Add to Cart</button>
        <div id="add-result"></div>
    </div>
    
    <div class="debug-section">
        <h2>Update Cart Item</h2>
        <input type="number" id="update-variant-id" placeholder="Variant ID">
        <input type="number" id="update-quantity" placeholder="New Quantity">
        <button onclick="updateCart()">Update Cart</button>
        <div id="update-result"></div>
    </div>
    
    <div class="debug-section">
        <h2>Clear Cart</h2>
        <button onclick="clearCart()">Clear All Items</button>
        <div id="clear-result"></div>
    </div>
    
    <div class="debug-section">
        <h2>Console Logs</h2>
        <div id="console-logs"></div>
    </div>

    <script>
        // Override console.log to display in page
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLogToPage('LOG', args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLogToPage('ERROR', args.join(' '));
        };
        
        function addLogToPage(type, message) {
            const logsDiv = document.getElementById('console-logs');
            const logEntry = document.createElement('div');
            logEntry.className = type === 'ERROR' ? 'result error' : 'result';
            logEntry.innerHTML = `<strong>[${type}]</strong> ${message}`;
            logsDiv.appendChild(logEntry);
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }
        
        async function checkCart() {
            const statusDiv = document.getElementById('cart-status');
            statusDiv.innerHTML = 'Checking cart...';
            
            try {
                const response = await fetch('/cart.js');
                console.log('Cart fetch response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const cart = await response.json();
                console.log('Cart data:', cart);
                
                let html = `<div class="result success">
                    <strong>Cart Status:</strong><br>
                    Items: ${cart.item_count}<br>
                    Total: ${cart.total_price}<br>
                    Currency: ${cart.currency}<br><br>
                    <strong>Items:</strong><br>`;
                
                if (cart.items && cart.items.length > 0) {
                    cart.items.forEach(item => {
                        html += `- ${item.product_title} (Variant: ${item.variant_id}, Qty: ${item.quantity}, Price: ${item.price})<br>`;
                    });
                } else {
                    html += 'No items in cart<br>';
                }
                
                html += '</div>';
                statusDiv.innerHTML = html;
                
            } catch (error) {
                console.error('Error checking cart:', error);
                statusDiv.innerHTML = `<div class="result error">Error: ${error.message}</div>`;
            }
        }
        
        async function addToCart() {
            const variantId = document.getElementById('variant-id').value;
            const quantity = document.getElementById('quantity').value || 1;
            const resultDiv = document.getElementById('add-result');
            
            if (!variantId) {
                resultDiv.innerHTML = '<div class="result error">Please enter a variant ID</div>';
                return;
            }
            
            resultDiv.innerHTML = 'Adding to cart...';
            
            try {
                console.log('Adding to cart:', { variantId, quantity });
                
                const response = await fetch('/cart/add.js', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        id: parseInt(variantId),
                        quantity: parseInt(quantity)
                    })
                });
                
                console.log('Add to cart response status:', response.status);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
                
                const result = await response.json();
                console.log('Add to cart result:', result);
                
                resultDiv.innerHTML = `<div class="result success">Successfully added to cart!</div>`;
                
                // Auto-refresh cart status
                setTimeout(checkCart, 500);
                
            } catch (error) {
                console.error('Error adding to cart:', error);
                resultDiv.innerHTML = `<div class="result error">Error: ${error.message}</div>`;
            }
        }
        
        async function updateCart() {
            const variantId = document.getElementById('update-variant-id').value;
            const quantity = document.getElementById('update-quantity').value;
            const resultDiv = document.getElementById('update-result');
            
            if (!variantId || quantity === '') {
                resultDiv.innerHTML = '<div class="result error">Please enter variant ID and quantity</div>';
                return;
            }
            
            resultDiv.innerHTML = 'Updating cart...';
            
            try {
                const updates = {};
                updates[variantId] = parseInt(quantity);
                
                const response = await fetch('/cart/update.js', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ updates })
                });
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
                
                const result = await response.json();
                console.log('Update cart result:', result);
                
                resultDiv.innerHTML = `<div class="result success">Cart updated successfully!</div>`;
                
                // Auto-refresh cart status
                setTimeout(checkCart, 500);
                
            } catch (error) {
                console.error('Error updating cart:', error);
                resultDiv.innerHTML = `<div class="result error">Error: ${error.message}</div>`;
            }
        }
        
        async function clearCart() {
            const resultDiv = document.getElementById('clear-result');
            resultDiv.innerHTML = 'Clearing cart...';
            
            try {
                // First get current cart
                const cartResponse = await fetch('/cart.js');
                const cart = await cartResponse.json();
                
                if (cart.items.length === 0) {
                    resultDiv.innerHTML = '<div class="result">Cart is already empty</div>';
                    return;
                }
                
                // Create updates object to set all items to 0
                const updates = {};
                cart.items.forEach(item => {
                    updates[item.variant_id] = 0;
                });
                
                const response = await fetch('/cart/update.js', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ updates })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                resultDiv.innerHTML = '<div class="result success">Cart cleared successfully!</div>';
                
                // Auto-refresh cart status
                setTimeout(checkCart, 500);
                
            } catch (error) {
                console.error('Error clearing cart:', error);
                resultDiv.innerHTML = `<div class="result error">Error: ${error.message}</div>`;
            }
        }
        
        // Auto-check cart on page load
        window.onload = function() {
            checkCart();
        };
    </script>
</body>
</html>
